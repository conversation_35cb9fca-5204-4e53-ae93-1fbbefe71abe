package com.fls.stock.pojo.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 班组库存锁定记录表(WorkteamStockLockRecord)表VO类
 *
 * <AUTHOR>
 * @since 2024-12-11 09:08:53
 */
@Data
public class TeamStockLockRecordVo {

    //库存锁定记录主键id
    @ExcelIgnore
    private String idLockRecord;

    //库存记录id
    @ExcelIgnore
    private String idStock;

    //经营主体id
    @ExcelIgnore
    private String idBizunit;

    //经营主体名称
    @ExcelProperty(value = "经营主体名称")
    private String bizunitName;

    //班组id
    @ExcelIgnore
    private String idWorkteam;

    //班组名称
    @ExcelProperty(value = "班组名称")
    private String workteamName;

    //仓库id
    @ExcelIgnore
    private String idWarehouse;

    //仓库名称
    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    //货位id
    @ExcelIgnore
    private String idWhpos;

    //货位名称
    @ExcelProperty(value = "货位名称")
    private String whposName;

    //批次号
    @ExcelProperty(value = "批次号")
    private String batchCode;

    //批次pk值
    @ExcelIgnore
    private String pkBatchCode;

    //物料id
    @ExcelIgnore
    private String idMaterial;

    //物料pk
    @ExcelIgnore
    private String pkMaterial;

    //物料编码
    @ExcelProperty(value = "物料编码")
    private String materialCode;

    //物料名称
    @ExcelProperty(value = "物料名称")
    private String materialName;

    //存货名称
    @ExcelProperty(value = "存货名称")
    private String inventoryName;

    //物料参数
    @ExcelProperty(value = "物料参数")
    private String materialParam;

    //数量
    @ExcelProperty(value = "数量")
    private BigDecimal lockNum;

    //NC计量单位主键
    @ExcelIgnore
    private String pkMeasdoc;

    //单位
    @ExcelProperty(value = "单位")
    private String unit;

    //项目名称
    @ExcelIgnore
    private String projectCode;

    //库存锁定来源单据编号
    @ExcelProperty(value = "库存锁定来源单据编号")
    private String sourceBillCode;

    //库存锁定来源单据id
    @ExcelIgnore
    private String idSourceBill;

    //来源单据资源id
    @ExcelIgnore
    private String idSourceRes;

    //来源资源名称
    @ExcelIgnore
    private String sourceResName;

    //源头来源单据id
    @ExcelIgnore
    private String idFirstSourceBill;

    //源头来源单据编号
    @ExcelProperty(value = "源头来源单据编号")
    private String firstSourceBillCode;

    //源头单据资源id
    @ExcelIgnore
    private String idFirstRes;

    //源头资源名称
    @ExcelProperty(value = "源头资源名称")
    private String firstResName;

    //操作时间
    @ExcelProperty(value = "操作时间")
    private LocalDateTime operateTime;

    @ExcelIgnore
    private String creator;

    @ExcelProperty(value = "创建人名称")
    private String createName;
}

