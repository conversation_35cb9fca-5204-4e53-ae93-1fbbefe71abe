package com.fls.stock.service.woekteam;

import com.fls.common.mybatis.core.page.PageResult;
import com.fls.stock.entity.WorkteamStock;
import com.fls.stock.entity.WorkteamStockLockRecord;
import com.fls.stock.pojo.model.BaseSourceBill;
import com.fls.stock.pojo.query.TeamStockLockQuery;
import com.fls.stock.pojo.vo.TeamStockLockRecordVo;
import com.github.yulichang.base.MPJBaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 班组库存锁定记录表(WorkteamStockLockRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-11 09:08:53
 */
public interface IWorkteamStockLockRecordService extends MPJBaseService<WorkteamStockLockRecord> {

    void createWorkteamStockLockRecord(BaseSourceBill sourceBill, List<WorkteamStock> stocks, boolean isLock);

    PageResult<TeamStockLockRecordVo> getLockRecordPage(TeamStockLockQuery lockQuery);

    /**
     * 校验释放库存操作是否有匹配的锁定记录
     *
     * @param sourceBillCode 来源单号
     * @return boolean结果
     */
    boolean checkReleaseLockRecord(String sourceBillCode);

    /**
     * 导出锁定记录
     *
     * @param lockQuery 锁定记录查询条件
     * @param response 响应
     */
    void exportLockRecord(TeamStockLockQuery lockQuery, HttpServletResponse response);
}

