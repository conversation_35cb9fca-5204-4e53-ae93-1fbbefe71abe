package com.fls.master.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.master.entity.BaseBizunitEntity;
import com.fls.master.entity.BaseDepartment;
import com.fls.master.entity.BaseGroup;
import com.fls.master.entity.BaseJob;
import com.fls.master.entity.BaseJobtype;
import com.fls.master.entity.BaseOrg;
import com.fls.master.entity.BasePerson;
import com.fls.master.entity.BasePost;
import com.fls.master.entity.BasePsnjobEntity;
import com.fls.master.entity.BaseUser;
import com.fls.master.entity.BaseWhposEntity;
import com.fls.master.entity.BaseWorkTeamMemberEntity;
import com.fls.master.pojo.query.DepartmentQuery;
import com.fls.master.pojo.query.OrgQuery;
import com.fls.master.pojo.query.PersonCodeQuery;
import com.fls.master.pojo.query.PersonQuery;
import com.fls.master.pojo.query.PostQuery;
import com.fls.master.pojo.query.StatusQuery;
import com.fls.master.pojo.query.UserRoleQuery;
import com.fls.master.pojo.query.WhposQuery;
import com.fls.master.pojo.vo.DepartmentVO;
import com.fls.master.pojo.vo.JobVO;
import com.fls.master.pojo.vo.OrgVO;
import com.fls.master.pojo.vo.PersonVO;
import com.fls.master.pojo.vo.PostVO;
import com.fls.master.pojo.vo.UserVO;
import com.fls.master.pojo.vo.WhposVO;
import com.fls.master.service.BaseDepartmentService;
import com.fls.master.service.BaseJobService;
import com.fls.master.service.BaseOrgService;
import com.fls.master.service.BasePersonService;
import com.fls.master.service.BasePostService;
import com.fls.master.service.BasePsnjobService;
import com.fls.master.service.BaseUserService;
import com.fls.master.service.BaseWhposService;
import com.fls.master.service.OrgStructManageService;
import com.fls.master.service.PermissionService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * OrgStructManageServiceImpl
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class OrgStructManageServiceImpl implements OrgStructManageService {

    private final PermissionService permissionService;

    private final BaseOrgService orgService;

    private final BaseDepartmentService departmentService;

    private final BasePostService postService;

    private final BaseJobService jobService;

    private final BasePersonService personService;

    private final BaseUserService userService;

    private final BaseWhposService whposService;

    private final BasePsnjobService psnjobService;

    @Override
    public List<OrgVO> orgPublicList(OrgQuery query) {
        MPJLambdaWrapper<BaseOrg> wrapper = getOrgBaseWrapper(query);
        return orgService.selectJoinList(OrgVO.class, wrapper);
    }

    @NotNull
    private static MPJLambdaWrapper<BaseOrg> getOrgBaseWrapper(OrgQuery query) {
        MPJLambdaWrapper<BaseOrg> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseOrg.class)
            .selectAs(BaseGroup::getName, OrgVO::getGroupName)
            .leftJoin(BaseGroup.class, BaseGroup::getIdGroup, BaseOrg::getIdGroup)
            .eq(BaseOrg::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BaseGroup::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseOrg::getStatus, query.getStatus())
            .eq(StrUtil.isNotBlank(query.getType()), BaseOrg::getOrgType, query.getType())
            .eq(StrUtil.isNotBlank(query.getIdParent()), BaseOrg::getIdParentorg, query.getIdParent());
        return wrapper;
    }

    @Override
    public List<OrgVO> orgPrivateList(OrgQuery query) {
        List<OrgVO> result = Lists.newArrayList();
        List<String> orgIds = permissionService.getPrivateOrgId(query.getUserId(), query.getHref());

        if (CollUtil.isNotEmpty(orgIds)) {
            MPJLambdaWrapper<BaseOrg> wrapper = getOrgBaseWrapper(query);
            wrapper.in(BaseOrg::getIdOrg, orgIds);
            result = orgService.selectJoinList(OrgVO.class, wrapper);
        }
        return result;
    }

    @Override
    public List<DepartmentVO> departmentPublicList(DepartmentQuery query) {
        MPJLambdaWrapper<BaseDepartment> wrapper = getDepartmentBaseWrapper(query);
        return departmentService.selectJoinList(DepartmentVO.class, wrapper);
    }

    private static MPJLambdaWrapper<BaseDepartment> getDepartmentBaseWrapper(DepartmentQuery query) {
        MPJLambdaWrapper<BaseDepartment> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseDepartment.class)
            .selectAs(BaseGroup::getName, DepartmentVO::getGroupName)
            .selectAs(BaseOrg::getName, DepartmentVO::getOrgName)
            .leftJoin(BaseGroup.class, BaseGroup::getIdGroup, BaseDepartment::getIdGroup)
            .leftJoin(BaseOrg.class, BaseOrg::getIdOrg, BaseDepartment::getIdOrg)
            .eq(BaseDepartment::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseDepartment::getStatus, query.getStatus())
            .eq(StrUtil.isNotBlank(query.getIdOrg()), BaseDepartment::getIdOrg, query.getIdOrg())
            .eq(StrUtil.isNotBlank(query.getIdParent()), BaseDepartment::getIdParentdept, query.getIdParent());
        return wrapper;
    }

    @Override
    public List<DepartmentVO> departmentPrivateList(DepartmentQuery query) {
        List<DepartmentVO> result = Lists.newArrayList();
        List<String> deptIds = permissionService.getPrivateDepartmentId(query.getUserId(), query.getHref());

        if (CollUtil.isNotEmpty(deptIds)) {
            MPJLambdaWrapper<BaseDepartment> wrapper = getDepartmentBaseWrapper(query);
            wrapper.in(BaseDepartment::getIdDepartment, deptIds);
            result = departmentService.selectJoinList(DepartmentVO.class, wrapper);
        }
        return result;
    }

    @Override
    public List<PostVO> postPublicList(PostQuery query) {
        MPJLambdaWrapper<BasePost> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BasePost.class)
            .selectAs(BaseGroup::getName, PostVO::getGroupName)
            .selectAs(BaseOrg::getName, PostVO::getOrgName)
            .selectAs(BaseDepartment::getName, PostVO::getDepartmentName)
            .selectAs(BaseJob::getName, PostVO::getJobName)
            .leftJoin(BaseGroup.class, BaseGroup::getIdGroup, BasePost::getIdGroup)
            .leftJoin(BaseOrg.class, BaseOrg::getIdOrg, BasePost::getIdOrg)
            .leftJoin(BaseDepartment.class, BaseDepartment::getIdDepartment, BasePost::getIdDepartment)
            .leftJoin(BaseJob.class, BaseJob::getIdJob, BasePost::getIdJob)
            .eq(BasePost::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BasePost::getStatus, query.getStatus())
            .eq(StrUtil.isNotBlank(query.getIdOrg()), BasePost::getIdOrg, query.getIdOrg())
            .eq(StrUtil.isNotBlank(query.getIdDept()), BasePost::getIdDepartment, query.getIdDept())
            .eq(StrUtil.isNotBlank(query.getIdParent()), BasePost::getIdParentpost, query.getIdParent());

        return postService.selectJoinList(PostVO.class, wrapper);
    }

    @Override
    public List<JobVO> jobPublicList(StatusQuery query) {
        MPJLambdaWrapper<BaseJob> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseJob.class)
            .selectAs(BaseJobtype::getName, JobVO::getJobtypeName)
            .leftJoin(BaseJobtype.class, BaseJobtype::getIdJobtype, BaseJob::getIdJobtype)
            .eq(BaseJob::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BaseJobtype::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(Objects.nonNull(query.getStatus()), BaseJob::getStatus, query.getStatus());

        return jobService.selectJoinList(JobVO.class, wrapper);
    }

    @Override
    public List<PersonVO> personPublicList(PersonQuery query) {
        Assert.notBlank(query.getIdOrg(), () -> new ServiceException("所属组织id不能为空"));
        MPJLambdaWrapper<BasePerson> wrapper = getBasePersonMPJLambdaWrapper(query);
        return personService.selectJoinList(PersonVO.class, wrapper);
    }

    private static MPJLambdaWrapper<BasePerson> getBasePersonMPJLambdaWrapper(PersonQuery query) {
        MPJLambdaWrapper<BasePerson> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BasePerson.class)
            .selectAs(BaseOrg::getName, PersonVO::getOrgName)
            .selectAs(BaseDepartment::getName, PersonVO::getDepartmentName)
            .selectAs(BasePost::getName, PersonVO::getPostName)
            .selectAs(BaseJob::getName, PersonVO::getJobName)
            .selectAs(BaseUser::getIdUser, PersonVO::getIdUser)
            .leftJoin(BaseOrg.class, BaseOrg::getIdOrg, BasePerson::getIdOrg)
            .leftJoin(BaseDepartment.class, BaseDepartment::getIdGroup, BasePerson::getIdDepartment)
            .leftJoin(BasePost.class, BasePost::getIdPost, BasePerson::getIdPost)
            .leftJoin(BaseJob.class, BaseJob::getIdJob, BasePerson::getIdJob)
            .leftJoin(BaseUser.class, BaseUser::getCode, BasePerson::getCode)
            .eq(BasePerson::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BasePerson::getPostFlag, CommonConstants.YES)
            .eq(Objects.nonNull(query.getStatus()), BasePerson::getStatus, query.getStatus())
            .eq(StrUtil.isNotBlank(query.getIdOrg()), BasePerson::getIdOrg, query.getIdOrg())
            .eq(StrUtil.isNotBlank(query.getIdDepartment()), BasePerson::getIdDepartment, query.getIdDepartment())
            .like(StrUtil.isNotBlank(query.getName()), BasePerson::getName, query.getName())
            .eq(StrUtil.isNotBlank(query.getIdPost()), BasePerson::getIdPost, query.getIdPost());
        //班组的查询逻辑因为涉及连接一对多，所以单独拆分出来
        if (StrUtil.isNotEmpty(query.getIdWorkteam())) {
            wrapper.leftJoin(BaseWorkTeamMemberEntity.class, BaseWorkTeamMemberEntity::getIdMember, BasePerson::getIdPerson)
                .eq(StrUtil.isNotBlank(query.getIdWorkteam()), BaseWorkTeamMemberEntity::getIdWorkteam, query.getIdWorkteam());
        }
        return wrapper;
    }

    @Override
    public List<PersonVO> personPrivateList(PersonQuery query) {
        List<PersonVO> result = Lists.newArrayList();
        List<String> privateOrgId = permissionService.getPrivateOrgId(query.getUserId(), query.getHref());

        if (CollUtil.isNotEmpty(privateOrgId)) {
            MPJLambdaWrapper<BasePerson> wrapper = getBasePersonMPJLambdaWrapper(query);
            wrapper.in(BasePerson::getIdOrg, privateOrgId);
            result = personService.selectJoinList(PersonVO.class, wrapper);
        }
        return result;
    }

    @Override
    public List<PersonVO> personListByCode(PersonCodeQuery query) {
        if (query.validateOrgOrBizunit()) {
            throw new ServiceException("所属组织和所属经营主体至少需要填写一个");
        }
        // 查询正式员工
        List<PersonVO> regularPersonList = getRegularPersonList(query);

        // 查询兼职人员
        List<PersonVO> partTimePersonList = getPartTimePersonList(query);
        // 合并结果并去重（根据员工ID去重）
        List<PersonVO> allPersonList = new ArrayList<>(regularPersonList);
        List<String> existingPersonIds = regularPersonList.stream()
            .map(PersonVO::getIdPerson)
            .collect(Collectors.toList());
        // 添加不重复的兼职人员
        for (PersonVO partTimePerson : partTimePersonList) {
            if (!existingPersonIds.contains(partTimePerson.getIdPerson())) {
                allPersonList.add(partTimePerson);
            }
        }
        return allPersonList;
    }

    /**
     * 查询正式员工
     */
    private List<PersonVO> getRegularPersonList(PersonCodeQuery query) {
        MPJLambdaWrapper<BasePerson> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BasePerson.class)
            .selectAs(BaseOrg::getName, PersonVO::getOrgName)
            .selectAs(BaseUser::getIdUser, PersonVO::getIdUser)
            .selectAs(BaseDepartment::getName, PersonVO::getDepartmentName)
            .selectAs(BasePost::getName, PersonVO::getPostName)
            .selectAs(BaseJob::getName, PersonVO::getJobName)
            .leftJoin(BaseOrg.class, BaseOrg::getIdOrg, BasePerson::getIdOrg)
            .leftJoin(BaseDepartment.class, BaseDepartment::getIdDepartment, BasePerson::getIdDepartment)
            .leftJoin(BasePost.class, BasePost::getIdPost, BasePerson::getIdPost)
            .leftJoin(BaseJob.class, BaseJob::getIdJob, BasePerson::getIdJob)
            .leftJoin(BaseBizunitEntity.class, BaseBizunitEntity::getIdOrg, BasePerson::getIdOrg)
            .leftJoin(BaseUser.class, BaseUser::getCode, BasePerson::getCode)
            .eq(BasePerson::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BasePerson::getStatus, CommonConstants.COMMON_STATUS_NORMAL)
            .eq(BasePerson::getPostFlag, CommonConstants.YES)
            .eq(StrUtil.isNotBlank(query.getIdOrg()), BasePerson::getIdOrg, query.getIdOrg())
            .eq(StrUtil.isNotBlank(query.getIdBizunit()), BaseBizunitEntity::getIdBizunit, query.getIdBizunit())
            .eq(StrUtil.isNotBlank(query.getJCode()), BaseJob::getCode, query.getJCode())
            .eq(StrUtil.isNotBlank(query.getPCode()), BasePost::getCode, query.getPCode());
        return personService.selectJoinList(PersonVO.class, wrapper);
    }

    /**
     * 查询兼职人员
     */
    private List<PersonVO> getPartTimePersonList(PersonCodeQuery query) {
        MPJLambdaWrapper<BasePsnjobEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BasePsnjobEntity.class)
            .selectAll(BasePerson.class)
            .selectAs(BaseOrg::getName, PersonVO::getOrgName)
            .selectAs(BaseUser::getIdUser, PersonVO::getIdUser)
            .selectAs(BaseDepartment::getName, PersonVO::getDepartmentName)
            .selectAs(BasePost::getName, PersonVO::getPostName)
            .selectAs(BaseJob::getName, PersonVO::getJobName)
            .leftJoin(BasePerson.class, BasePerson::getIdPerson, BasePsnjobEntity::getIdPerson)
            .leftJoin(BaseOrg.class, BaseOrg::getIdOrg, BasePsnjobEntity::getIdOrg)
            .leftJoin(BaseDepartment.class, BaseDepartment::getIdDepartment, BasePsnjobEntity::getIdDepartment)
            .leftJoin(BasePost.class, BasePost::getIdPost, BasePsnjobEntity::getIdPost)
            .leftJoin(BaseJob.class, BaseJob::getIdJob, BasePsnjobEntity::getIdJob)
            .leftJoin(BaseBizunitEntity.class, BaseBizunitEntity::getIdOrg, BasePsnjobEntity::getIdOrg)
            .leftJoin(BaseUser.class, BaseUser::getCode, BasePerson::getCode)
            .eq(BasePsnjobEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BasePerson::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BasePerson::getStatus, CommonConstants.COMMON_STATUS_NORMAL)
            .eq(BasePerson::getPostFlag, CommonConstants.YES)
            .eq(StrUtil.isNotBlank(query.getIdOrg()), BasePsnjobEntity::getIdOrg, query.getIdOrg())
            .eq(StrUtil.isNotBlank(query.getIdBizunit()), BaseBizunitEntity::getIdBizunit, query.getIdBizunit())
            .eq(StrUtil.isNotBlank(query.getJCode()), BaseJob::getCode, query.getJCode())
            .eq(StrUtil.isNotBlank(query.getPCode()), BasePost::getCode, query.getPCode())
            // 兼职人员的查询条件：结束时间为空或者结束时间大于当前时间，表示还在兼职
            .and(w -> w.isNull(BasePsnjobEntity::getEndDate)
                .or()
                .gt(BasePsnjobEntity::getEndDate, LocalDate.now()));

        return psnjobService.selectJoinList(PersonVO.class, wrapper);
    }

    @Override
    public List<UserVO> userListByRole(UserRoleQuery query) {
        if (query.validateRoleParam()) {
            throw new ServiceException("角色id或者角色编码至少需要填写一个");
        }
        return userService.getUsersByRole(query);
    }

    @Override
    public List<WhposVO> whposList(WhposQuery query) {
        MPJLambdaWrapper<BaseWhposEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseWhposEntity.class)
            .eq(BasePerson::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(BasePerson::getStatus, CommonConstants.COMMON_STATUS_NORMAL)
            .eq(StrUtil.isNotBlank(query.getIdWarehouse()), BaseWhposEntity::getIdWarehouse, query.getIdWarehouse())
            .apply("CHAR_LENGTH(code) = 10");
        return whposService.selectJoinList(WhposVO.class, wrapper);
    }
}
